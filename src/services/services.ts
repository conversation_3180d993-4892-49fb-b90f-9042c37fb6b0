import httpService from './http-service';
import {
  MostlyUsedTemplateResponse,
  TemplateListResponse,
  TemplateUserResponse,
} from '../types/template';
import {RiskListResponse} from '../types/risk';
import {
  CommonStringOptionResponse,
  CrewMember,
  OfficeItem,
  VesselData,
} from '../types';

export const getRiskCategoryList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/categories`);

  return data;
};

export const getHazardsList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/hazards`);

  return data;
};

export const getRiskParameterType = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/parameters`);
  return data;
};

export const getMainRiskParameterType = async (
  isRequiredForRiskRating?: boolean,
) => {
  const params = isRequiredForRiskRating
    ? {is_required_for_risk_rating: true}
    : undefined;
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/parameter-types`, {
      params,
    });

  return data;
};

export const getTaskReliabilityAssessList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/task-reliability-assessments`);

  return data;
};

export const generateQueryParams = (key: string, valuesArray: string[]) => {
  return valuesArray
    .map(value => `${key}=${encodeURIComponent(value)}`)
    .join(`&`);
};

export const GET_VESSEL_USER_DETAILS = (key = 'group', ids = ['TechD']) => {
  const queryString = `returnAttribute=ship_party_id&${generateQueryParams(
    key,
    ids,
  )}`;

  return `/keycloak-admin/users?${queryString}`;
};

// Endpoint identifier for template listing
const TEMPLATE_LIST_ENDPOINT = 'template-list';

export const getTemplateList = async (
  params: {
    page?: number;
    limit?: number;
  } & Record<string, string | number | string[]>,
) => {
  const signal = httpService.cancelPreviousRequest(TEMPLATE_LIST_ENDPOINT);
  const {data} = await httpService
    .getAxiosClient()
    .get<TemplateListResponse>(
      `${process.env.API_RISK_ASSESSMENT_URL}/templates`,
      {
        params,
        signal, // Use the abort signal
      },
    );
  return data.result;
};

export const getTemplateUserList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get<TemplateUserResponse>(
      `${process.env.API_RISK_ASSESSMENT_URL}/templates/users`,
    );

  return data.result;
};

export const markTemplateAsArchived = async (templateId: number) => {
  const {data} = await httpService
    .getAxiosClient()
    .patch(
      `${process.env.API_RISK_ASSESSMENT_URL}/templates/${templateId}/inactive`,
    );
  return data;
};

export const getMostlyUsedTemplates = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get<MostlyUsedTemplateResponse>(
      `${process.env.API_RISK_ASSESSMENT_URL}/templates/top?maxCount=4`,
    );
  return data.result;
};

// Endpoint identifier for template listing
const RA_LIST_ENDPOINT = 'template-list';

export const getRAList = async (
  params: {
    page?: number;
    limit?: number;
  } & Record<string, string | number | string[]>,
) => {
  const signal = httpService.cancelPreviousRequest(RA_LIST_ENDPOINT);
  const {data} = await httpService
    .getAxiosClient()
    .get<RiskListResponse>(`${process.env.API_RISK_ASSESSMENT_URL}/risks`, {
      params,
      signal, // Use the abort signal
    });
  return data.result;
};

export const getRAStringOptions = async (
  optionType: 'vessel_category' | 'vessel_tech_group',
) => {
  const {data} = await httpService
    .getAxiosClient()
    .get<CommonStringOptionResponse>(
      `${process.env.API_RISK_ASSESSMENT_URL}/risks/options/${optionType}`,
    );

  return data;
};

export const getTemplateById = async (id: string) => {
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/templates/${id}`);

  return data;
};
export const getRiskById = async (id: string) => {
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/risks/${id}`);

  return data;
};

export const deleteTemplateById = async (id: number) => {
  const {data} = await httpService
    .getAxiosClient()
    .delete(`${process.env.API_RISK_ASSESSMENT_URL}/templates/${id}`);

  return data;
};

export const deleteRiskById = async (id: number) => {
  const {data} = await httpService
    .getAxiosClient()
    .delete(`${process.env.API_RISK_ASSESSMENT_URL}/risks/${id}`);

  return data;
};

export const createNewTemplate = async (payload: any) => {
  const {data} = await httpService
    .getAxiosClient()
    .post(`${process.env.API_RISK_ASSESSMENT_URL}/templates`, payload);

  return data;
};
export const updateSavedTemplate = async (
  id: number | string,
  payload: any,
) => {
  const {data} = await httpService
    .getAxiosClient()
    .patch(`${process.env.API_RISK_ASSESSMENT_URL}/templates/${id}`, payload);

  return data;
};

export const getVesselsList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get<VesselData[]>(
      `${process.env.API_RISK_ASSESSMENT_URL}/vessel-ownership`,
    );
  return data;
};

export const getOfficesList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get<OfficeItem[]>(
      `${process.env.API_RISK_ASSESSMENT_URL}/reporting-office`,
    );
  return data;
};

export const getCrewList = async (vesselId: number) => {
  const {data} = await httpService
    .getAxiosClient()
    .get(`${process.env.API_RISK_ASSESSMENT_URL}/crew-list`, {
      params: {
        vessel_id: vesselId,
      },
    });
  return data.crewList as CrewMember[];
};

export const createNewRA = async (payload: any) => {
  const {data} = await httpService
    .getAxiosClient()
    .post(`${process.env.API_RISK_ASSESSMENT_URL}/risks`, payload);

  return data;
};
export const updateSavedRA = async (id: number | string, payload: any) => {
  const {data} = await httpService
    .getAxiosClient()
    .patch(`${process.env.API_RISK_ASSESSMENT_URL}/risks/${id}`, payload);

  return data;
};

export const getApprovalsRequiredList = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get<any[]>(`${process.env.API_RISK_ASSESSMENT_URL}/approval-required`);
  return data;
};

export const getSeafarerRanks = async () => {
  const {data} = await httpService
    .getAxiosClient()
    .get<any[]>(
      `${process.env.API_RISK_ASSESSMENT_URL}/seafarer-lookup?values=ranks`,
    );
  return data;
};
